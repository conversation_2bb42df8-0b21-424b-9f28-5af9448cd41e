import { describe, it, expect } from 'vitest';
import {
    checkCollision,
    calculateDistance,
    clamp,
    lerp,
    degreesToRadians,
    radiansToDegrees,
    randomBetween,
    applyPhysics,
    updateInputFromKeyboard,
    easeInOutQuad,
    easeOutBounce,
    calculateScreenShake,
    createBananaParticles,
    updateParticles,
    type Particle
} from './gameUtils';
import type { Sprite, GameInput } from '../types';

describe('gameUtils', () => {
    describe('checkCollision', () => {
        it('should detect collision between overlapping sprites', () => {
            const sprite1: Sprite = { x: 0, y: 0, width: 50, height: 50, texture: 'test', visible: true };
            const sprite2: Sprite = { x: 25, y: 25, width: 50, height: 50, texture: 'test', visible: true };

            expect(checkCollision(sprite1, sprite2)).toBe(true);
        });

        it('should not detect collision between non-overlapping sprites', () => {
            const sprite1: Sprite = { x: 0, y: 0, width: 50, height: 50, texture: 'test', visible: true };
            const sprite2: Sprite = { x: 100, y: 100, width: 50, height: 50, texture: 'test', visible: true };

            expect(checkCollision(sprite1, sprite2)).toBe(false);
        });

        it('should detect collision when sprites are touching edges', () => {
            const sprite1: Sprite = { x: 0, y: 0, width: 50, height: 50, texture: 'test', visible: true };
            const sprite2: Sprite = { x: 50, y: 0, width: 50, height: 50, texture: 'test', visible: true };

            expect(checkCollision(sprite1, sprite2)).toBe(false); // Touching edges don't overlap
        });

        it('should detect collision when one sprite is inside another', () => {
            const sprite1: Sprite = { x: 0, y: 0, width: 100, height: 100, texture: 'test', visible: true };
            const sprite2: Sprite = { x: 25, y: 25, width: 25, height: 25, texture: 'test', visible: true };

            expect(checkCollision(sprite1, sprite2)).toBe(true);
        });
    });

    describe('calculateDistance', () => {
        it('should calculate distance between two points', () => {
            const distance = calculateDistance(0, 0, 3, 4);
            expect(distance).toBe(5); // 3-4-5 triangle
        });

        it('should calculate distance for same point', () => {
            const distance = calculateDistance(5, 5, 5, 5);
            expect(distance).toBe(0);
        });

        it('should calculate distance with negative coordinates', () => {
            const distance = calculateDistance(-3, -4, 0, 0);
            expect(distance).toBe(5);
        });
    });

    describe('clamp', () => {
        it('should clamp value below minimum', () => {
            expect(clamp(5, 10, 20)).toBe(10);
        });

        it('should clamp value above maximum', () => {
            expect(clamp(25, 10, 20)).toBe(20);
        });

        it('should not clamp value within range', () => {
            expect(clamp(15, 10, 20)).toBe(15);
        });

        it('should handle edge cases', () => {
            expect(clamp(10, 10, 20)).toBe(10);
            expect(clamp(20, 10, 20)).toBe(20);
        });
    });

    describe('lerp', () => {
        it('should interpolate between two values', () => {
            expect(lerp(0, 10, 0.5)).toBe(5);
        });

        it('should return start value when factor is 0', () => {
            expect(lerp(10, 20, 0)).toBe(10);
        });

        it('should return end value when factor is 1', () => {
            expect(lerp(10, 20, 1)).toBe(20);
        });

        it('should handle negative values', () => {
            expect(lerp(-10, 10, 0.5)).toBe(0);
        });
    });

    describe('degreesToRadians', () => {
        it('should convert degrees to radians', () => {
            expect(degreesToRadians(180)).toBeCloseTo(Math.PI);
            expect(degreesToRadians(90)).toBeCloseTo(Math.PI / 2);
            expect(degreesToRadians(0)).toBe(0);
            expect(degreesToRadians(360)).toBeCloseTo(2 * Math.PI);
        });
    });

    describe('radiansToDegrees', () => {
        it('should convert radians to degrees', () => {
            expect(radiansToDegrees(Math.PI)).toBeCloseTo(180);
            expect(radiansToDegrees(Math.PI / 2)).toBeCloseTo(90);
            expect(radiansToDegrees(0)).toBe(0);
            expect(radiansToDegrees(2 * Math.PI)).toBeCloseTo(360);
        });
    });

    describe('randomBetween', () => {
        it('should generate number within range', () => {
            for (let i = 0; i < 100; i++) {
                const result = randomBetween(5, 10);
                expect(result).toBeGreaterThanOrEqual(5);
                expect(result).toBeLessThanOrEqual(10);
            }
        });

        it('should handle same min and max', () => {
            const result = randomBetween(5, 5);
            expect(result).toBe(5);
        });

        it('should handle negative ranges', () => {
            for (let i = 0; i < 100; i++) {
                const result = randomBetween(-10, -5);
                expect(result).toBeGreaterThanOrEqual(-10);
                expect(result).toBeLessThanOrEqual(-5);
            }
        });
    });

    describe('applyPhysics', () => {
        it('should apply gravity to vertical velocity', () => {
            const position = { x: 0, y: 0 };
            const velocity = { x: 0, y: 0 };

            const result = applyPhysics(position, velocity, 0.5, 0.8, 1);

            expect(result.velocity.y).toBe(0.5); // Gravity applied
        });

        it('should apply friction to horizontal velocity', () => {
            const position = { x: 0, y: 0 };
            const velocity = { x: 10, y: 0 };

            const result = applyPhysics(position, velocity, 0.5, 0.8, 1);

            expect(result.velocity.x).toBe(8); // 10 * 0.8 friction
        });

        it('should update position based on velocity', () => {
            const position = { x: 0, y: 0 };
            const velocity = { x: 5, y: 3 };

            const result = applyPhysics(position, velocity, 0, 1, 1);

            expect(result.position.x).toBe(5);
            expect(result.position.y).toBe(3);
        });

        it('should handle custom deltaTime', () => {
            const position = { x: 0, y: 0 };
            const velocity = { x: 10, y: 0 };

            const result = applyPhysics(position, velocity, 0.5, 0.8, 2);

            expect(result.position.x).toBe(16); // 10 * 0.8 * 2
            expect(result.velocity.y).toBe(1); // 0.5 * 2
        });
    });

    describe('updateInputFromKeyboard', () => {
        it('should map arrow keys to input', () => {
            const input: GameInput = { left: false, right: false, up: false, down: false, jump: false, interact: false };
            const keysPressed = new Set(['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown']);

            const result = updateInputFromKeyboard(input, keysPressed);

            expect(result.left).toBe(true);
            expect(result.right).toBe(true);
            expect(result.up).toBe(true);
            expect(result.down).toBe(true);
            expect(result.jump).toBe(false);
            expect(result.interact).toBe(false);
        });

        it('should map WASD keys to input', () => {
            const input: GameInput = { left: false, right: false, up: false, down: false, jump: false, interact: false };
            const keysPressed = new Set(['KeyA', 'KeyD', 'KeyW', 'KeyS']);

            const result = updateInputFromKeyboard(input, keysPressed);

            expect(result.left).toBe(true);
            expect(result.right).toBe(true);
            expect(result.up).toBe(true);
            expect(result.down).toBe(true);
        });

        it('should map space and enter keys', () => {
            const input: GameInput = { left: false, right: false, up: false, down: false, jump: false, interact: false };
            const keysPressed = new Set(['Space', 'Enter', 'KeyE']);

            const result = updateInputFromKeyboard(input, keysPressed);

            expect(result.jump).toBe(true);
            expect(result.interact).toBe(true);
        });

        it('should handle empty key set', () => {
            const input: GameInput = { left: false, right: false, up: false, down: false, jump: false, interact: false };
            const keysPressed = new Set<string>();

            const result = updateInputFromKeyboard(input, keysPressed);

            expect(result.left).toBe(false);
            expect(result.right).toBe(false);
            expect(result.up).toBe(false);
            expect(result.down).toBe(false);
            expect(result.jump).toBe(false);
            expect(result.interact).toBe(false);
        });
    });

    describe('easeInOutQuad', () => {
        it('should return 0 for t=0', () => {
            expect(easeInOutQuad(0)).toBe(0);
        });

        it('should return 1 for t=1', () => {
            expect(easeInOutQuad(1)).toBe(1);
        });

        it('should return 0.5 for t=0.5', () => {
            expect(easeInOutQuad(0.5)).toBe(0.5);
        });

        it('should handle values between 0 and 1', () => {
            const result = easeInOutQuad(0.25);
            expect(result).toBeGreaterThan(0);
            expect(result).toBeLessThan(0.5);
        });
    });

    describe('easeOutBounce', () => {
        it('should return 0 for t=0', () => {
            expect(easeOutBounce(0)).toBe(0);
        });

        it('should return 1 for t=1', () => {
            expect(easeOutBounce(1)).toBeCloseTo(1, 5);
        });

        it('should handle different bounce phases', () => {
            const result1 = easeOutBounce(0.1);
            const result2 = easeOutBounce(0.5);
            const result3 = easeOutBounce(0.9);

            expect(result1).toBeGreaterThan(0);
            expect(result2).toBeGreaterThan(result1);
            expect(result3).toBeGreaterThan(result2);
        });
    });

    describe('calculateScreenShake', () => {
        it('should return zero shake after duration', () => {
            const result = calculateScreenShake(10, 1, 2);
            expect(result.x).toBe(0);
            expect(result.y).toBe(0);
        });

        it('should return shake values within intensity range', () => {
            const intensity = 10;
            const result = calculateScreenShake(intensity, 1, 0.5);

            expect(Math.abs(result.x)).toBeLessThanOrEqual(intensity / 2);
            expect(Math.abs(result.y)).toBeLessThanOrEqual(intensity / 2);
        });

        it('should decrease intensity over time', () => {
            const intensity = 10;
            const duration = 1;

            const early = calculateScreenShake(intensity, duration, 0.1);
            const late = calculateScreenShake(intensity, duration, 0.9);

            // Later shake should generally be smaller (though random, so we test multiple times)
            let earlyLarger = 0;
            for (let i = 0; i < 100; i++) {
                const e = calculateScreenShake(intensity, duration, 0.1);
                const l = calculateScreenShake(intensity, duration, 0.9);
                if (Math.abs(e.x) + Math.abs(e.y) > Math.abs(l.x) + Math.abs(l.y)) {
                    earlyLarger++;
                }
            }
            expect(earlyLarger).toBeGreaterThan(50); // Should be true most of the time
        });
    });

    describe('createBananaParticles', () => {
        it('should create specified number of particles', () => {
            const particles = createBananaParticles(100, 200, 5);
            expect(particles).toHaveLength(5);
        });

        it('should create particles at specified position', () => {
            const particles = createBananaParticles(100, 200, 3);
            particles.forEach(particle => {
                expect(particle.x).toBe(100);
                expect(particle.y).toBe(200);
            });
        });

        it('should create particles with random velocities', () => {
            const particles = createBananaParticles(0, 0, 10);

            // Check that particles have different velocities (randomness)
            const velocities = particles.map(p => ({ vx: p.vx, vy: p.vy }));
            const uniqueVelocities = new Set(velocities.map(v => `${v.vx},${v.vy}`));
            expect(uniqueVelocities.size).toBeGreaterThan(1); // Should have some variety
        });

        it('should create particles with correct properties', () => {
            const particles = createBananaParticles(0, 0, 1);
            const particle = particles[0];

            expect(particle.life).toBe(60);
            expect(particle.maxLife).toBe(60);
            expect(particle.color).toBe(0xFFD700);
            expect(particle.scale).toBeGreaterThan(0);
            expect(particle.vx).toBeGreaterThanOrEqual(-3);
            expect(particle.vx).toBeLessThanOrEqual(3);
            expect(particle.vy).toBeGreaterThanOrEqual(-5);
            expect(particle.vy).toBeLessThanOrEqual(-1);
        });

        it('should use default count when not specified', () => {
            const particles = createBananaParticles(0, 0);
            expect(particles).toHaveLength(10);
        });
    });

    describe('updateParticles', () => {
        it('should update particle positions', () => {
            const particles: Particle[] = [{
                x: 0, y: 0, vx: 5, vy: -3, life: 30, maxLife: 60, color: 0xFFD700, scale: 1
            }];

            const updated = updateParticles(particles);

            expect(updated[0].x).toBe(5);
            expect(updated[0].y).toBe(-3);
        });

        it('should apply gravity to particles', () => {
            const particles: Particle[] = [{
                x: 0, y: 0, vx: 0, vy: -5, life: 30, maxLife: 60, color: 0xFFD700, scale: 1
            }];

            const updated = updateParticles(particles);

            expect(updated[0].vy).toBe(-4.9); // -5 + 0.1 gravity
        });

        it('should decrease particle life', () => {
            const particles: Particle[] = [{
                x: 0, y: 0, vx: 0, vy: 0, life: 30, maxLife: 60, color: 0xFFD700, scale: 1
            }];

            const updated = updateParticles(particles);

            expect(updated[0].life).toBe(29);
        });

        it('should shrink particles over time', () => {
            const particles: Particle[] = [{
                x: 0, y: 0, vx: 0, vy: 0, life: 30, maxLife: 60, color: 0xFFD700, scale: 1
            }];

            const updated = updateParticles(particles);

            expect(updated[0].scale).toBe(0.98);
        });

        it('should remove dead particles', () => {
            const particles: Particle[] = [
                { x: 0, y: 0, vx: 0, vy: 0, life: 1, maxLife: 60, color: 0xFFD700, scale: 1 },
                { x: 0, y: 0, vx: 0, vy: 0, life: 10, maxLife: 60, color: 0xFFD700, scale: 1 }
            ];

            const updated = updateParticles(particles);

            // After update: life 1 becomes 0 (removed), life 10 becomes 9 (kept)
            expect(updated).toHaveLength(1); // One particle should be removed
            expect(updated.every(p => p.life > 0)).toBe(true);
            expect(updated[0].life).toBe(9);
        });

        it('should handle empty particle array', () => {
            const updated = updateParticles([]);
            expect(updated).toEqual([]);
        });

        it('should handle multiple update cycles', () => {
            let particles: Particle[] = [{
                x: 0, y: 0, vx: 1, vy: -2, life: 5, maxLife: 60, color: 0xFFD700, scale: 1
            }];

            // Update multiple times
            particles = updateParticles(particles);
            particles = updateParticles(particles);
            particles = updateParticles(particles);

            expect(particles[0].x).toBe(3); // 1 + 1 + 1
            expect(particles[0].y).toBeCloseTo(-5.7, 1); // Approximate due to floating point
            expect(particles[0].life).toBe(2); // 5 - 3
        });
    });
});
