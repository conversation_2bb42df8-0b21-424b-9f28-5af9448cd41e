import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { questStore, activeQuests, completedQuests } from './questStore';
import type { Quest } from '../types';

describe('questStore', () => {
    beforeEach(() => {
        // Reset store to default state with sample quests
        questStore.set([
            {
                id: 'daily-tasks-3',
                title: 'Daily Productivity',
                description: 'Complete 3 tasks today',
                type: 'daily',
                requirements: [
                    {
                        type: 'complete_tasks',
                        target: 3,
                        current: 0
                    }
                ],
                bananaReward: 25,
                completed: false,
                progress: 0,
                maxProgress: 3
            },
            {
                id: 'weekly-streak-7',
                title: 'Consistency Champion',
                description: 'Maintain a 7-day task completion streak',
                type: 'weekly',
                requirements: [
                    {
                        type: 'maintain_streak',
                        target: 7,
                        current: 0
                    }
                ],
                bananaReward: 100,
                completed: false,
                progress: 0,
                maxProgress: 7
            },
            {
                id: 'achievement-100-tasks',
                title: 'Century Club',
                description: 'Complete 100 tasks total',
                type: 'achievement',
                requirements: [
                    {
                        type: 'complete_tasks',
                        target: 100,
                        current: 0
                    }
                ],
                bananaReward: 500,
                completed: false,
                progress: 0,
                maxProgress: 100
            }
        ]);
    });

    describe('initial state', () => {
        it('should have default quests loaded', () => {
            const quests = get(questStore);
            expect(quests).toHaveLength(3);
            expect(quests.find(q => q.id === 'daily-tasks-3')).toBeDefined();
            expect(quests.find(q => q.id === 'weekly-streak-7')).toBeDefined();
            expect(quests.find(q => q.id === 'achievement-100-tasks')).toBeDefined();
        });

        it('should have all quests initially incomplete', () => {
            const quests = get(questStore);
            const allIncomplete = quests.every(quest => !quest.completed);
            expect(allIncomplete).toBe(true);
        });
    });

    describe('updateProgress', () => {
        it('should update quest progress', () => {
            questStore.updateProgress('daily-tasks-3', 2);
            const quests = get(questStore);
            const quest = quests.find(q => q.id === 'daily-tasks-3');
            
            expect(quest?.progress).toBe(2);
            expect(quest?.requirements[0].current).toBe(2);
            expect(quest?.completed).toBe(false);
        });

        it('should complete quest when progress reaches max', () => {
            questStore.updateProgress('daily-tasks-3', 3);
            const quests = get(questStore);
            const quest = quests.find(q => q.id === 'daily-tasks-3');
            
            expect(quest?.progress).toBe(3);
            expect(quest?.completed).toBe(true);
            expect(quest?.requirements[0].current).toBe(3);
        });

        it('should not exceed max progress', () => {
            questStore.updateProgress('daily-tasks-3', 5);
            const quests = get(questStore);
            const quest = quests.find(q => q.id === 'daily-tasks-3');
            
            expect(quest?.progress).toBe(3); // Should be capped at maxProgress
            expect(quest?.completed).toBe(true);
        });

        it('should not affect other quests', () => {
            questStore.updateProgress('daily-tasks-3', 2);
            const quests = get(questStore);
            const weeklyQuest = quests.find(q => q.id === 'weekly-streak-7');
            const achievementQuest = quests.find(q => q.id === 'achievement-100-tasks');
            
            expect(weeklyQuest?.progress).toBe(0);
            expect(achievementQuest?.progress).toBe(0);
        });
    });

    describe('completeQuest', () => {
        it('should mark quest as completed', () => {
            questStore.completeQuest('weekly-streak-7');
            const quests = get(questStore);
            const quest = quests.find(q => q.id === 'weekly-streak-7');
            
            expect(quest?.completed).toBe(true);
            expect(quest?.progress).toBe(quest?.maxProgress);
        });

        it('should not affect other quests', () => {
            questStore.completeQuest('weekly-streak-7');
            const quests = get(questStore);
            const dailyQuest = quests.find(q => q.id === 'daily-tasks-3');
            const achievementQuest = quests.find(q => q.id === 'achievement-100-tasks');
            
            expect(dailyQuest?.completed).toBe(false);
            expect(achievementQuest?.completed).toBe(false);
        });
    });

    describe('resetDailyQuests', () => {
        it('should reset only daily quests', () => {
            // First, make some progress and complete some quests
            questStore.updateProgress('daily-tasks-3', 3);
            questStore.updateProgress('weekly-streak-7', 5);
            questStore.completeQuest('achievement-100-tasks');
            
            // Reset daily quests
            questStore.resetDailyQuests();
            
            const quests = get(questStore);
            const dailyQuest = quests.find(q => q.id === 'daily-tasks-3');
            const weeklyQuest = quests.find(q => q.id === 'weekly-streak-7');
            const achievementQuest = quests.find(q => q.id === 'achievement-100-tasks');
            
            // Daily quest should be reset
            expect(dailyQuest?.completed).toBe(false);
            expect(dailyQuest?.progress).toBe(0);
            expect(dailyQuest?.requirements[0].current).toBe(0);
            
            // Other quests should remain unchanged
            expect(weeklyQuest?.progress).toBe(5);
            expect(achievementQuest?.completed).toBe(true);
        });
    });

    describe('resetWeeklyQuests', () => {
        it('should reset only weekly quests', () => {
            // First, make some progress and complete some quests
            questStore.updateProgress('daily-tasks-3', 2);
            questStore.updateProgress('weekly-streak-7', 7);
            questStore.completeQuest('achievement-100-tasks');
            
            // Reset weekly quests
            questStore.resetWeeklyQuests();
            
            const quests = get(questStore);
            const dailyQuest = quests.find(q => q.id === 'daily-tasks-3');
            const weeklyQuest = quests.find(q => q.id === 'weekly-streak-7');
            const achievementQuest = quests.find(q => q.id === 'achievement-100-tasks');
            
            // Weekly quest should be reset
            expect(weeklyQuest?.completed).toBe(false);
            expect(weeklyQuest?.progress).toBe(0);
            expect(weeklyQuest?.requirements[0].current).toBe(0);
            
            // Other quests should remain unchanged
            expect(dailyQuest?.progress).toBe(2);
            expect(achievementQuest?.completed).toBe(true);
        });
    });
});

describe('activeQuests derived store', () => {
    beforeEach(() => {
        questStore.set([
            {
                id: 'quest1',
                title: 'Active Quest',
                description: 'This quest is active',
                type: 'daily',
                requirements: [{ type: 'complete_tasks', target: 5, current: 2 }],
                bananaReward: 50,
                completed: false,
                progress: 2,
                maxProgress: 5
            },
            {
                id: 'quest2',
                title: 'Completed Quest',
                description: 'This quest is completed',
                type: 'weekly',
                requirements: [{ type: 'complete_tasks', target: 10, current: 10 }],
                bananaReward: 100,
                completed: true,
                progress: 10,
                maxProgress: 10
            }
        ]);
    });

    it('should return only incomplete quests', () => {
        const active = get(activeQuests);
        expect(active).toHaveLength(1);
        expect(active[0].id).toBe('quest1');
        expect(active[0].completed).toBe(false);
    });

    it('should update when quest completion status changes', () => {
        questStore.completeQuest('quest1');
        const active = get(activeQuests);
        expect(active).toHaveLength(0);
    });
});

describe('completedQuests derived store', () => {
    beforeEach(() => {
        questStore.set([
            {
                id: 'quest1',
                title: 'Active Quest',
                description: 'This quest is active',
                type: 'daily',
                requirements: [{ type: 'complete_tasks', target: 5, current: 2 }],
                bananaReward: 50,
                completed: false,
                progress: 2,
                maxProgress: 5
            },
            {
                id: 'quest2',
                title: 'Completed Quest',
                description: 'This quest is completed',
                type: 'weekly',
                requirements: [{ type: 'complete_tasks', target: 10, current: 10 }],
                bananaReward: 100,
                completed: true,
                progress: 10,
                maxProgress: 10
            }
        ]);
    });

    it('should return only completed quests', () => {
        const completed = get(completedQuests);
        expect(completed).toHaveLength(1);
        expect(completed[0].id).toBe('quest2');
        expect(completed[0].completed).toBe(true);
    });

    it('should update when quest completion status changes', () => {
        questStore.completeQuest('quest1');
        const completed = get(completedQuests);
        expect(completed).toHaveLength(2);
    });
});
