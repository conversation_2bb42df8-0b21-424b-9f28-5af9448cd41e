import { describe, it, expect, beforeEach } from 'vitest';
import { BananaCalculator } from './bananaCalculator';
import type { Task } from '$lib/types';

describe('BananaCalculator', () => {
    let calculator: BananaCalculator;

    beforeEach(() => {
        // Reset singleton instance for each test
        (BananaCalculator as any).instance = null;
        calculator = BananaCalculator.getInstance();
    });

    describe('getInstance', () => {
        it('should return the same instance (singleton pattern)', () => {
            const instance1 = BananaCalculator.getInstance();
            const instance2 = BananaCalculator.getInstance();
            expect(instance1).toBe(instance2);
        });
    });

    describe('calculateTaskReward', () => {
        it('should calculate basic reward for simple task', () => {
            const task: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Simple task',
                description: 'A basic task',
                priority: 'medium',
                completed: false,
                bananaReward: 0
            };

            const reward = calculator.calculateTaskReward(task);
            expect(reward).toBe(12); // 10 (medium) + 2 (description)
            expect(typeof reward).toBe('number');
        });

        it('should give higher rewards for high priority tasks', () => {
            const lowPriorityTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Low priority task',
                priority: 'low',
                completed: false,
                bananaReward: 0
            };

            const highPriorityTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'High priority task',
                priority: 'high',
                completed: false,
                bananaReward: 0
            };

            const lowReward = calculator.calculateTaskReward(lowPriorityTask);
            const highReward = calculator.calculateTaskReward(highPriorityTask);

            expect(highReward).toBeGreaterThan(lowReward);
            expect(lowReward).toBe(5); // low priority base
            expect(highReward).toBe(15); // high priority base
        });

        it('should give higher rewards for tasks with due dates', () => {
            const taskWithoutDueDate: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task without due date',
                priority: 'medium',
                completed: false,
                bananaReward: 0
            };

            const taskWithDueDate: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task with due date',
                priority: 'medium',
                dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // tomorrow
                completed: false,
                bananaReward: 0
            };

            const rewardWithoutDueDate = calculator.calculateTaskReward(taskWithoutDueDate);
            const rewardWithDueDate = calculator.calculateTaskReward(taskWithDueDate);

            expect(rewardWithDueDate).toBeGreaterThan(rewardWithoutDueDate);
            expect(rewardWithoutDueDate).toBe(10); // medium priority only
            expect(rewardWithDueDate).toBe(11); // medium priority + due date bonus
        });

        it('should give bonus for descriptions', () => {
            const taskWithoutDesc: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task',
                priority: 'medium',
                completed: false,
                bananaReward: 0
            };

            const taskWithDesc: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task',
                description: 'This has a description',
                priority: 'medium',
                completed: false,
                bananaReward: 0
            };

            const rewardWithoutDesc = calculator.calculateTaskReward(taskWithoutDesc);
            const rewardWithDesc = calculator.calculateTaskReward(taskWithDesc);

            expect(rewardWithDesc).toBeGreaterThan(rewardWithoutDesc);
            expect(rewardWithoutDesc).toBe(10); // medium priority only
            expect(rewardWithDesc).toBe(12); // medium priority + description bonus
        });

        it('should handle tasks with categories', () => {
            const taskWithCategory: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Categorized task',
                priority: 'medium',
                category: 'Work',
                completed: false,
                bananaReward: 0
            };

            const reward = calculator.calculateTaskReward(taskWithCategory);
            expect(reward).toBe(11); // medium priority + category bonus
        });

        it('should return 0 for task without priority', () => {
            const emptyTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Empty task',
                priority: 'low', // We need to provide a priority since it's required
                completed: false,
                bananaReward: 0
            };
            const reward = calculator.calculateTaskReward(emptyTask);
            expect(reward).toBe(5); // Low priority base reward
        });
    });

    describe('calculateFeatureUnlockCost', () => {
        it('should return correct cost for known features', () => {
            const categoriesCost = calculator.calculateFeatureUnlockCost('categories');
            expect(categoriesCost).toBe(100);

            const customThemesCost = calculator.calculateFeatureUnlockCost('custom-themes');
            expect(customThemesCost).toBe(2500);
        });

        it('should return default cost for unknown features', () => {
            const unknownCost = calculator.calculateFeatureUnlockCost('unknownFeature' as any);
            expect(unknownCost).toBe(100);
        });

        it('should scale cost with user level', () => {
            const level1Cost = calculator.calculateFeatureUnlockCost('categories', 1);
            const level5Cost = calculator.calculateFeatureUnlockCost('categories', 5);

            expect(level5Cost).toBeGreaterThan(level1Cost);
        });
    });

    describe('calculateUpgradeCost', () => {
        it('should return correct cost for known upgrades', () => {
            const fasterMonkeyCost = calculator.calculateUpgradeCost('faster-monkey', 0);
            expect(fasterMonkeyCost).toBe(250);

            const doubleJumpCost = calculator.calculateUpgradeCost('double-jump', 0);
            expect(doubleJumpCost).toBe(750);
        });

        it('should scale cost with level', () => {
            const level0Cost = calculator.calculateUpgradeCost('faster-monkey', 0);
            const level1Cost = calculator.calculateUpgradeCost('faster-monkey', 1);
            const level2Cost = calculator.calculateUpgradeCost('faster-monkey', 2);

            expect(level1Cost).toBeGreaterThan(level0Cost);
            expect(level2Cost).toBeGreaterThan(level1Cost);
        });

        it('should return default cost for unknown upgrades', () => {
            const unknownCost = calculator.calculateUpgradeCost('unknownUpgrade' as any, 0);
            expect(unknownCost).toBe(500); // Default base cost
        });

        it('should handle level 0', () => {
            const level0Cost = calculator.calculateUpgradeCost('faster-monkey', 0);
            expect(level0Cost).toBe(250); // Should return base cost
        });
    });

    describe('applyMultipliers', () => {
        it('should apply multipliers correctly', () => {
            const baseReward = 100;
            const upgrades: any[] = []; // Empty upgrades
            const result = calculator.applyMultipliers(baseReward, upgrades, 0);
            expect(result).toBe(100); // No multipliers applied
        });

        it('should apply streak bonus', () => {
            const baseReward = 100;
            const upgrades: any[] = [];
            const result = calculator.applyMultipliers(baseReward, upgrades, 10);
            expect(result).toBe(150); // 50% streak bonus for 10 days
        });
    });

    describe('calculateNextMilestone', () => {
        it('should find next affordable milestone', () => {
            const milestone = calculator.calculateNextMilestone(50, []);
            expect(milestone).toEqual({
                feature: 'categories',
                cost: 100,
                remaining: 50
            });
        });

        it('should return null when all features are unlocked', () => {
            const allFeatures = ['categories', 'due-dates', 'priority-tags', 'habit-tracking',
                'analytics', 'export-options', 'collaboration', 'calendar-sync', 'custom-themes'];
            const milestone = calculator.calculateNextMilestone(10000, allFeatures);
            expect(milestone).toBeNull();
        });
    });
});
