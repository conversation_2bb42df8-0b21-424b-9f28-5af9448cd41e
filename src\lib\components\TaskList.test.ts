import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { taskStore } from '$lib/stores';
import type { Task } from '$lib/types';

/**
 * Tests for TaskList component logic
 * These tests focus on the business logic and state management
 * without relying on DOM rendering or component mounting
 */

// Helper class to simulate TaskList component logic
class TaskListLogic {
    showCompleted = false;
    sortBy: 'created' | 'priority' | 'dueDate' | 'title' = 'created';
    filterCategory = '';

    get tasks() {
        return get(taskStore);
    }

    get pendingTasks() {
        return this.tasks.filter((task: Task) => !task.completed);
    }

    get completedTasks() {
        return this.tasks.filter((task: Task) => task.completed);
    }

    get categories() {
        return [...new Set(this.tasks.map((task: Task) => task.category).filter(Boolean))];
    }

    get filteredTasks() {
        let filtered = this.showCompleted ? this.tasks : this.pendingTasks;

        // Filter by category
        if (this.filterCategory) {
            filtered = filtered.filter((task: Task) => task.category === this.filterCategory);
        }

        // Sort tasks
        return filtered.sort((a: Task, b: Task) => {
            switch (this.sortBy) {
                case 'priority': {
                    const priorityOrder = { high: 3, medium: 2, low: 1 };
                    return priorityOrder[b.priority] - priorityOrder[a.priority];
                }
                case 'dueDate':
                    if (!a.dueDate && !b.dueDate) return 0;
                    if (!a.dueDate) return 1;
                    if (!b.dueDate) return -1;
                    return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
                case 'title':
                    return a.title.localeCompare(b.title);
                case 'created':
                default:
                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
            }
        });
    }

    clearCompleted() {
        this.completedTasks.forEach((task: Task) => {
            taskStore.delete(task.id);
        });
    }

    getEmptyStateMessage() {
        if (this.tasks.length === 0) {
            return '🎯 No tasks yet! Add your first task above to get started.';
        } else if (this.filterCategory) {
            return `📂 No tasks found in "${this.filterCategory}" category.`;
        } else if (this.showCompleted) {
            return '✅ No completed tasks to show.';
        } else {
            return '🎉 All tasks completed! Great job!';
        }
    }
}

// Helper function to create test tasks
function createTestTask(overrides: Partial<Task> = {}): Task {
    return {
        id: Math.random().toString(36).substr(2, 9),
        title: 'Test Task',
        description: 'Test Description',
        priority: 'medium',
        category: 'Test',
        dueDate: undefined,
        completed: false,
        bananaReward: 10,
        createdAt: new Date(),
        ...overrides
    };
}

describe('TaskList Component Logic', () => {
    let listLogic: TaskListLogic;

    beforeEach(() => {
        // Clear task store
        taskStore.clear();

        // Create list logic instance
        listLogic = new TaskListLogic();
    });

    describe('Task Filtering', () => {
        beforeEach(() => {
            // Add test tasks
            taskStore.add(createTestTask({ title: 'Pending Task 1', completed: false }));
            taskStore.add(createTestTask({ title: 'Pending Task 2', completed: false }));
            taskStore.add(createTestTask({ title: 'Completed Task 1', completed: true }));
            taskStore.add(createTestTask({ title: 'Completed Task 2', completed: true }));
        });

        it('should show only pending tasks by default', () => {
            expect(listLogic.showCompleted).toBe(false);
            expect(listLogic.filteredTasks.length).toBe(2);
            expect(listLogic.filteredTasks.every(task => !task.completed)).toBe(true);
        });

        it('should show all tasks when showCompleted is true', () => {
            listLogic.showCompleted = true;
            expect(listLogic.filteredTasks.length).toBe(4);
        });

        it('should correctly count pending and completed tasks', () => {
            expect(listLogic.pendingTasks.length).toBe(2);
            expect(listLogic.completedTasks.length).toBe(2);
        });
    });

    describe('Category Filtering', () => {
        beforeEach(() => {
            taskStore.add(createTestTask({ title: 'Work Task 1', category: 'Work' }));
            taskStore.add(createTestTask({ title: 'Work Task 2', category: 'Work' }));
            taskStore.add(createTestTask({ title: 'Personal Task', category: 'Personal' }));
            taskStore.add(createTestTask({ title: 'No Category Task', category: '' }));
        });

        it('should extract unique categories', () => {
            const categories = listLogic.categories;
            expect(categories).toContain('Work');
            expect(categories).toContain('Personal');
            expect(categories.length).toBe(2); // Empty categories are filtered out
        });

        it('should filter by selected category', () => {
            listLogic.filterCategory = 'Work';
            const filtered = listLogic.filteredTasks;
            expect(filtered.length).toBe(2);
            expect(filtered.every(task => task.category === 'Work')).toBe(true);
        });

        it('should show all tasks when no category filter is set', () => {
            listLogic.filterCategory = '';
            expect(listLogic.filteredTasks.length).toBe(4);
        });

        it('should return empty array for non-existent category', () => {
            listLogic.filterCategory = 'NonExistent';
            expect(listLogic.filteredTasks.length).toBe(0);
        });
    });

    describe('Task Sorting', () => {
        beforeEach(() => {
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
            const halfDayAgo = new Date(now.getTime() - 12 * 60 * 60 * 1000);

            // Add tasks with explicit createdAt timestamps to ensure proper sorting
            // Note: taskStore.add() will override createdAt, so we need to update after adding
            const zTask = taskStore.add(createTestTask({
                title: 'Z Task',
                priority: 'low',
                dueDate: tomorrow
            }));
            taskStore.update(zTask.id, { createdAt: yesterday }); // Oldest

            const mTask = taskStore.add(createTestTask({
                title: 'M Task',
                priority: 'medium',
                dueDate: undefined
            }));
            taskStore.update(mTask.id, { createdAt: halfDayAgo }); // Middle

            const aTask = taskStore.add(createTestTask({
                title: 'A Task',
                priority: 'high',
                dueDate: yesterday
            }));
            taskStore.update(aTask.id, { createdAt: now }); // Newest
        });

        it('should sort by creation date (newest first) by default', () => {
            listLogic.sortBy = 'created';
            const sorted = listLogic.filteredTasks;
            expect(sorted[0].title).toBe('A Task'); // Most recent (newest)
            expect(sorted[2].title).toBe('Z Task'); // Oldest
        });

        it('should sort by priority (high to low)', () => {
            listLogic.sortBy = 'priority';
            const sorted = listLogic.filteredTasks;
            expect(sorted[0].priority).toBe('high');
            expect(sorted[1].priority).toBe('medium');
            expect(sorted[2].priority).toBe('low');
        });

        it('should sort by title alphabetically', () => {
            listLogic.sortBy = 'title';
            const sorted = listLogic.filteredTasks;
            expect(sorted[0].title).toBe('A Task');
            expect(sorted[1].title).toBe('M Task');
            expect(sorted[2].title).toBe('Z Task');
        });

        it('should sort by due date (earliest first)', () => {
            listLogic.sortBy = 'dueDate';
            const sorted = listLogic.filteredTasks;
            expect(sorted[0].title).toBe('A Task'); // Yesterday (earliest)
            expect(sorted[1].title).toBe('Z Task'); // Tomorrow
            expect(sorted[2].title).toBe('M Task'); // No due date (last)
        });

        it('should handle tasks without due dates in date sorting', () => {
            taskStore.clear();
            taskStore.add(createTestTask({ title: 'No Date 1', dueDate: undefined }));
            taskStore.add(createTestTask({ title: 'No Date 2', dueDate: undefined }));

            listLogic.sortBy = 'dueDate';
            const sorted = listLogic.filteredTasks;
            expect(sorted.length).toBe(2);
            // Both tasks should be present, order doesn't matter for tasks without dates
        });
    });

    describe('Combined Filtering and Sorting', () => {
        beforeEach(() => {
            taskStore.add(createTestTask({
                title: 'Work High',
                category: 'Work',
                priority: 'high',
                completed: false
            }));
            taskStore.add(createTestTask({
                title: 'Work Low',
                category: 'Work',
                priority: 'low',
                completed: false
            }));
            taskStore.add(createTestTask({
                title: 'Personal High',
                category: 'Personal',
                priority: 'high',
                completed: false
            }));
            taskStore.add(createTestTask({
                title: 'Work Completed',
                category: 'Work',
                priority: 'high',
                completed: true
            }));
        });

        it('should filter by category and sort by priority', () => {
            listLogic.filterCategory = 'Work';
            listLogic.sortBy = 'priority';
            listLogic.showCompleted = false;

            const filtered = listLogic.filteredTasks;
            expect(filtered.length).toBe(2);
            expect(filtered[0].title).toBe('Work High');
            expect(filtered[1].title).toBe('Work Low');
        });

        it('should include completed tasks when showCompleted is true', () => {
            listLogic.filterCategory = 'Work';
            listLogic.showCompleted = true;

            const filtered = listLogic.filteredTasks;
            expect(filtered.length).toBe(3);
            expect(filtered.some(task => task.completed)).toBe(true);
        });
    });

    describe('Clear Completed Tasks', () => {
        beforeEach(() => {
            taskStore.add(createTestTask({ title: 'Pending 1', completed: false }));
            taskStore.add(createTestTask({ title: 'Completed 1', completed: true }));
            taskStore.add(createTestTask({ title: 'Completed 2', completed: true }));
            taskStore.add(createTestTask({ title: 'Pending 2', completed: false }));
        });

        it('should remove all completed tasks', () => {
            expect(listLogic.completedTasks.length).toBe(2);
            expect(listLogic.pendingTasks.length).toBe(2);

            listLogic.clearCompleted();

            expect(listLogic.completedTasks.length).toBe(0);
            expect(listLogic.pendingTasks.length).toBe(2);
            expect(listLogic.tasks.length).toBe(2);
        });

        it('should not affect pending tasks', () => {
            const pendingTasksBefore = listLogic.pendingTasks.map(t => t.title);

            listLogic.clearCompleted();

            const pendingTasksAfter = listLogic.pendingTasks.map(t => t.title);
            expect(pendingTasksAfter).toEqual(pendingTasksBefore);
        });

        it('should handle case with no completed tasks', () => {
            taskStore.clear();
            taskStore.add(createTestTask({ completed: false }));

            expect(() => listLogic.clearCompleted()).not.toThrow();
            expect(listLogic.tasks.length).toBe(1);
        });
    });

    describe('Empty State Messages', () => {
        it('should show correct message when no tasks exist', () => {
            expect(listLogic.getEmptyStateMessage()).toBe('🎯 No tasks yet! Add your first task above to get started.');
        });

        it('should show correct message when filtering by category with no results', () => {
            taskStore.add(createTestTask({ category: 'Work' }));
            listLogic.filterCategory = 'Personal';

            expect(listLogic.getEmptyStateMessage()).toBe('📂 No tasks found in "Personal" category.');
        });

        it('should show correct message when showing completed tasks but none exist', () => {
            taskStore.add(createTestTask({ completed: false }));
            listLogic.showCompleted = true;
            listLogic.filterCategory = '';

            // This would show all tasks, but if we filter to only completed, we'd get empty
            // For this test, let's simulate the scenario where filteredTasks would be empty
            // when showing only completed tasks
            expect(listLogic.getEmptyStateMessage()).toBe('✅ No completed tasks to show.');
        });

        it('should show correct message when all tasks are completed', () => {
            taskStore.add(createTestTask({ completed: true }));
            listLogic.showCompleted = false; // Only showing pending

            expect(listLogic.getEmptyStateMessage()).toBe('🎉 All tasks completed! Great job!');
        });
    });

    describe('Edge Cases', () => {
        it('should handle empty task store', () => {
            expect(listLogic.tasks.length).toBe(0);
            expect(listLogic.pendingTasks.length).toBe(0);
            expect(listLogic.completedTasks.length).toBe(0);
            expect(listLogic.categories.length).toBe(0);
            expect(listLogic.filteredTasks.length).toBe(0);
        });

        it('should handle tasks with null/undefined categories', () => {
            taskStore.add(createTestTask({ category: '' }));
            taskStore.add(createTestTask({ category: undefined as any }));
            taskStore.add(createTestTask({ category: null as any }));
            taskStore.add(createTestTask({ category: 'Valid' }));

            const categories = listLogic.categories;
            expect(categories).toEqual(['Valid']);
        });

        it('should handle tasks with same creation time', () => {
            const sameTime = new Date();
            taskStore.add(createTestTask({ title: 'Task 1', createdAt: sameTime }));
            taskStore.add(createTestTask({ title: 'Task 2', createdAt: sameTime }));

            listLogic.sortBy = 'created';
            const sorted = listLogic.filteredTasks;
            expect(sorted.length).toBe(2);
        });

        it('should handle rapid filter changes', () => {
            taskStore.add(createTestTask({ category: 'Work' }));
            taskStore.add(createTestTask({ category: 'Personal' }));

            listLogic.filterCategory = 'Work';
            expect(listLogic.filteredTasks.length).toBe(1);

            listLogic.filterCategory = 'Personal';
            expect(listLogic.filteredTasks.length).toBe(1);

            listLogic.filterCategory = '';
            expect(listLogic.filteredTasks.length).toBe(2);
        });

        it('should handle special characters in categories', () => {
            taskStore.add(createTestTask({ category: '🎯 Work & Stuff!' }));
            taskStore.add(createTestTask({ category: 'Wörk ñ Stüff' }));

            const categories = listLogic.categories;
            expect(categories).toContain('🎯 Work & Stuff!');
            expect(categories).toContain('Wörk ñ Stüff');

            listLogic.filterCategory = '🎯 Work & Stuff!';
            expect(listLogic.filteredTasks.length).toBe(1);
        });
    });
});
