import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { userStore } from '$lib/stores';

// Helper functions to simulate the main page component logic
class MainPageLogic {
    showTaskForm = false;
    showTaskList = false;
    debugMode = false;

    toggleTaskForm() {
        this.showTaskForm = !this.showTaskForm;
        if (this.showTaskForm) this.showTaskList = false; // Close other overlay
    }

    toggleTaskList() {
        this.showTaskList = !this.showTaskList;
        if (this.showTaskList) this.showTaskForm = false; // Close other overlay
    }

    toggleDebugMode() {
        this.debugMode = !this.debugMode;
    }

    closeOverlays() {
        this.showTaskForm = false;
        this.showTaskList = false;
    }
}

describe('Main Page Component Logic', () => {
    let pageLogic: MainPageLogic;

    beforeEach(() => {
        // Reset userStore to default state
        userStore.set({
            id: crypto.randomUUID(),
            bananaCount: 42,
            totalTasksCompleted: 5,
            totalGoalsCompleted: 2,
            unlockedFeatures: ['basic-tasks'],
            isPremium: false,
            createdAt: new Date(),
            lastActiveAt: new Date()
        });

        // Create fresh instance of page logic
        pageLogic = new MainPageLogic();
    });

    describe('Store Integration', () => {
        it('should have correct initial userStore values', () => {
            const user = get(userStore);
            expect(user.bananaCount).toBe(42);
            expect(user.totalTasksCompleted).toBe(5);
            expect(user.totalGoalsCompleted).toBe(2);
            expect(user.unlockedFeatures).toContain('basic-tasks');
            expect(user.isPremium).toBe(false);
        });

        it('should react to userStore changes', () => {
            const initialUser = get(userStore);
            expect(initialUser.bananaCount).toBe(42);

            // Update userStore
            userStore.addBananas(8);

            const updatedUser = get(userStore);
            expect(updatedUser.bananaCount).toBe(50);
        });

        it('should update last active time when adding bananas', () => {
            const initialUser = get(userStore);
            const initialTime = initialUser.lastActiveAt;

            // Wait a bit to ensure time difference
            setTimeout(() => {
                userStore.addBananas(5);
                const updatedUser = get(userStore);
                expect(updatedUser.lastActiveAt.getTime()).toBeGreaterThan(initialTime.getTime());
            }, 10);
        });
    });

    describe('Toggle Button Functionality', () => {
        it('should toggle task form overlay', () => {
            // Initially, overlay should not be visible
            expect(pageLogic.showTaskForm).toBe(false);

            // Toggle to show overlay
            pageLogic.toggleTaskForm();
            expect(pageLogic.showTaskForm).toBe(true);

            // Toggle again to hide overlay
            pageLogic.toggleTaskForm();
            expect(pageLogic.showTaskForm).toBe(false);
        });

        it('should toggle task list overlay', () => {
            // Initially, overlay should not be visible
            expect(pageLogic.showTaskList).toBe(false);

            // Toggle to show overlay
            pageLogic.toggleTaskList();
            expect(pageLogic.showTaskList).toBe(true);

            // Toggle again to hide overlay
            pageLogic.toggleTaskList();
            expect(pageLogic.showTaskList).toBe(false);
        });

        it('should toggle debug mode', () => {
            // Initially, debug mode should be off
            expect(pageLogic.debugMode).toBe(false);

            // Toggle to enable debug mode
            pageLogic.toggleDebugMode();
            expect(pageLogic.debugMode).toBe(true);

            // Toggle again to disable debug mode
            pageLogic.toggleDebugMode();
            expect(pageLogic.debugMode).toBe(false);
        });

        it('should close task list when task form is opened', () => {
            // Open task list first
            pageLogic.toggleTaskList();
            expect(pageLogic.showTaskList).toBe(true);
            expect(pageLogic.showTaskForm).toBe(false);

            // Open task form - should close task list
            pageLogic.toggleTaskForm();
            expect(pageLogic.showTaskList).toBe(false);
            expect(pageLogic.showTaskForm).toBe(true);
        });

        it('should close task form when task list is opened', () => {
            // Open task form first
            pageLogic.toggleTaskForm();
            expect(pageLogic.showTaskForm).toBe(true);
            expect(pageLogic.showTaskList).toBe(false);

            // Open task list - should close task form
            pageLogic.toggleTaskList();
            expect(pageLogic.showTaskForm).toBe(false);
            expect(pageLogic.showTaskList).toBe(true);
        });
    });

    describe('Overlay Management', () => {
        it('should close overlays when closeOverlays is called', () => {
            // Open both overlays
            pageLogic.showTaskForm = true;
            pageLogic.showTaskList = true;

            // Close overlays
            pageLogic.closeOverlays();

            // Both should be closed
            expect(pageLogic.showTaskForm).toBe(false);
            expect(pageLogic.showTaskList).toBe(false);
        });

        it('should handle multiple overlay state changes correctly', () => {
            // Start with both closed
            expect(pageLogic.showTaskForm).toBe(false);
            expect(pageLogic.showTaskList).toBe(false);

            // Open task form
            pageLogic.toggleTaskForm();
            expect(pageLogic.showTaskForm).toBe(true);
            expect(pageLogic.showTaskList).toBe(false);

            // Open task list (should close task form)
            pageLogic.toggleTaskList();
            expect(pageLogic.showTaskForm).toBe(false);
            expect(pageLogic.showTaskList).toBe(true);

            // Close all overlays
            pageLogic.closeOverlays();
            expect(pageLogic.showTaskForm).toBe(false);
            expect(pageLogic.showTaskList).toBe(false);
        });

        it('should maintain debug mode state independently of overlays', () => {
            // Enable debug mode
            pageLogic.toggleDebugMode();
            expect(pageLogic.debugMode).toBe(true);

            // Open and close overlays
            pageLogic.toggleTaskForm();
            pageLogic.closeOverlays();

            // Debug mode should still be enabled
            expect(pageLogic.debugMode).toBe(true);
        });
    });

    describe('Component State Logic', () => {
        it('should have correct initial state', () => {
            expect(pageLogic.showTaskForm).toBe(false);
            expect(pageLogic.showTaskList).toBe(false);
            expect(pageLogic.debugMode).toBe(false);
        });

        it('should handle rapid toggle operations', () => {
            // Rapidly toggle task form
            pageLogic.toggleTaskForm();
            pageLogic.toggleTaskForm();
            pageLogic.toggleTaskForm();
            expect(pageLogic.showTaskForm).toBe(true);

            // Rapidly toggle task list
            pageLogic.toggleTaskList();
            pageLogic.toggleTaskList();
            pageLogic.toggleTaskList();
            expect(pageLogic.showTaskList).toBe(true);
            expect(pageLogic.showTaskForm).toBe(false); // Should be closed by task list
        });
    });
});
