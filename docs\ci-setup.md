# CI/CD Setup Documentation

## Overview

This document describes the Continuous Integration (CI) setup for the Banana Checklist project. The CI pipeline ensures code quality through automated testing, linting, coverage reporting, and build verification.

## Components

### 1. GitHub Actions Workflow (`.github/workflows/ci.yml`)

The CI workflow runs on:
- Push to `main` and `develop` branches
- Pull requests to `main` and `develop` branches

#### Jobs

**Test Job (`test`)**
- Runs on Ubuntu with Node.js 18.x and 20.x
- Installs dependencies
- Runs TypeScript check (non-blocking)
- Runs ESLint (non-blocking)
- Executes tests with coverage
- Uploads coverage to Codecov
- Builds the project
- Uploads build artifacts

**Lint Check Job (`lint-check`)**
- Verifies code formatting
- Checks if `npm run lint:fix` would make changes
- Fails if auto-fixable linting issues are found

**Coverage Check Job (`coverage-check`)**
- Runs test coverage
- Comments coverage report on PRs
- Enforces minimum coverage thresholds

### 2. ESLint Configuration (`eslint.config.js`)

**Features:**
- TypeScript support with `typescript-eslint`
- Svelte component linting with `eslint-plugin-svelte`
- <PERSON>rowser and Node.js globals
- Test file specific configurations
- Relaxed rules for test files

**Key Rules:**
- No unused variables (with underscore prefix exception)
- Prefer const over let
- No console statements (warning)
- No debugger statements
- Svelte-specific rules for security and best practices

### 3. Test Coverage (`vitest.config.ts`)

**Current Thresholds:**
- Lines: 30%
- Functions: 30%
- Branches: 30%
- Statements: 30%

**Goal:** 80% coverage across all metrics

**Coverage Exclusions:**
- `node_modules/`
- `src/test/`
- `**/*.d.ts`
- `**/*.config.*`
- `build/`
- `.svelte-kit/`

## Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run preview         # Preview production build

# Testing
npm run test            # Run tests in watch mode
npm run test:run        # Run tests once
npm run test:coverage   # Run tests with coverage report
npm run test:ui         # Run tests with UI

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Run ESLint with auto-fix
npm run check           # Run TypeScript and Svelte checks
```

## Current Status

### ✅ Working
- Test execution with Vitest
- Coverage reporting (30% threshold)
- ESLint configuration
- Build process
- GitHub Actions workflow

### ⚠️ Known Issues
- TypeScript check has 63 errors (non-blocking in CI)
- ESLint has 14 issues (non-blocking in CI)
- Some missing component files referenced in exports

### 🎯 Next Steps

1. **Increase Test Coverage**
   - Add tests for components (`GameCanvas.svelte`, `TaskForm.svelte`, etc.)
   - Add tests for stores (`gameStore.ts`, `questStore.ts`, etc.)
   - Add tests for utilities (`gameUtils.ts`, `taskUtils.ts`)

2. **Fix TypeScript Issues**
   - Resolve type mismatches in components
   - Add missing component files
   - Fix test type issues

3. **Improve Code Quality**
   - Fix ESLint issues
   - Add missing Svelte component keys
   - Remove unused imports

4. **Gradually Increase Coverage Thresholds**
   ```typescript
   // In vitest.config.ts, gradually increase these values:
   thresholds: {
     global: {
       branches: 50,  // Increase from 30%
       functions: 50, // Increase from 30%
       lines: 50,     // Increase from 30%
       statements: 50 // Increase from 30%
     }
   }
   ```

## Coverage Improvement Strategy

1. **Phase 1 (Current):** 30% coverage baseline
2. **Phase 2:** Increase to 50% by adding component tests
3. **Phase 3:** Increase to 70% by adding integration tests
4. **Phase 4:** Reach 80% target with comprehensive test suite

## Monitoring

- Coverage reports are generated in `./coverage/` directory
- PR comments show coverage status
- Codecov integration provides detailed coverage analysis
- Build artifacts are stored for 7 days

## Contributing

When contributing:
1. Ensure tests pass: `npm run test:run`
2. Check coverage: `npm run test:coverage`
3. Fix linting issues: `npm run lint:fix`
4. Verify build: `npm run build`

The CI will automatically run these checks on your PR.
