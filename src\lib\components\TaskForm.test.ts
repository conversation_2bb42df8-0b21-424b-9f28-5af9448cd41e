import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { taskStore } from '$lib/stores';
import { BananaCalculator } from '$lib/utils/bananaCalculator';

/**
 * Tests for TaskForm component logic
 * These tests focus on the business logic and state management
 * without relying on DOM rendering or component mounting
 */

// Helper class to simulate TaskForm component logic
class TaskFormLogic {
  title = '';
  description = '';
  priority: 'low' | 'medium' | 'high' = 'medium';
  category = '';
  dueDate = '';

  get bananaReward() {
    return BananaCalculator.getInstance().calculateTaskReward({
      title: this.title,
      description: this.description,
      priority: this.priority,
      category: this.category,
      dueDate: this.dueDate ? new Date(this.dueDate) : undefined,
      completed: false,
      bananaReward: 0
    });
  }

  handleSubmit() {
    if (!this.title.trim()) return false;
    
    const newTask = {
      title: this.title.trim(),
      description: this.description.trim(),
      priority: this.priority,
      category: this.category.trim(),
      dueDate: this.dueDate ? new Date(this.dueDate) : undefined,
      completed: false,
      bananaReward: this.bananaReward
    };

    taskStore.add(newTask);
    
    // Reset form
    this.title = '';
    this.description = '';
    this.priority = 'medium';
    this.category = '';
    this.dueDate = '';
    
    return true;
  }

  handleKeydown(key: string, ctrlKey = false, metaKey = false) {
    if (key === 'Enter' && (ctrlKey || metaKey)) {
      return this.handleSubmit();
    }
    return false;
  }

  isSubmitDisabled() {
    return !this.title.trim();
  }
}

describe('TaskForm Component Logic', () => {
  let formLogic: TaskFormLogic;

  beforeEach(() => {
    // Clear task store
    taskStore.clear();
    
    // Create fresh instance of form logic
    formLogic = new TaskFormLogic();
  });

  describe('Form State Management', () => {
    it('should initialize with correct default values', () => {
      expect(formLogic.title).toBe('');
      expect(formLogic.description).toBe('');
      expect(formLogic.priority).toBe('medium');
      expect(formLogic.category).toBe('');
      expect(formLogic.dueDate).toBe('');
    });

    it('should update form fields correctly', () => {
      formLogic.title = 'Test Task';
      formLogic.description = 'Test Description';
      formLogic.priority = 'high';
      formLogic.category = 'Work';
      formLogic.dueDate = '2024-12-31';

      expect(formLogic.title).toBe('Test Task');
      expect(formLogic.description).toBe('Test Description');
      expect(formLogic.priority).toBe('high');
      expect(formLogic.category).toBe('Work');
      expect(formLogic.dueDate).toBe('2024-12-31');
    });

    it('should calculate banana reward reactively', () => {
      // Basic task
      formLogic.title = 'Simple task';
      expect(formLogic.bananaReward).toBeGreaterThan(0);

      // High priority task should have higher reward
      const basicReward = formLogic.bananaReward;
      formLogic.priority = 'high';
      expect(formLogic.bananaReward).toBeGreaterThan(basicReward);

      // Task with description should have higher reward
      formLogic.description = 'Detailed description of the task';
      expect(formLogic.bananaReward).toBeGreaterThan(basicReward);
    });
  });

  describe('Form Validation', () => {
    it('should require title to be non-empty', () => {
      expect(formLogic.isSubmitDisabled()).toBe(true);

      formLogic.title = '   '; // Only whitespace
      expect(formLogic.isSubmitDisabled()).toBe(true);

      formLogic.title = 'Valid title';
      expect(formLogic.isSubmitDisabled()).toBe(false);
    });

    it('should prevent submission with empty title', () => {
      const initialTaskCount = get(taskStore).length;
      
      const result = formLogic.handleSubmit();
      expect(result).toBe(false);
      expect(get(taskStore).length).toBe(initialTaskCount);
    });

    it('should prevent submission with whitespace-only title', () => {
      const initialTaskCount = get(taskStore).length;
      
      formLogic.title = '   ';
      const result = formLogic.handleSubmit();
      expect(result).toBe(false);
      expect(get(taskStore).length).toBe(initialTaskCount);
    });
  });

  describe('Task Creation', () => {
    it('should create task with minimal required data', () => {
      formLogic.title = 'Test Task';
      
      const result = formLogic.handleSubmit();
      expect(result).toBe(true);

      const tasks = get(taskStore);
      expect(tasks.length).toBe(1);
      expect(tasks[0].title).toBe('Test Task');
      expect(tasks[0].description).toBe('');
      expect(tasks[0].priority).toBe('medium');
      expect(tasks[0].category).toBe('');
      expect(tasks[0].dueDate).toBeUndefined();
      expect(tasks[0].completed).toBe(false);
      expect(tasks[0].bananaReward).toBeGreaterThan(0);
    });

    it('should create task with all fields populated', () => {
      formLogic.title = 'Complete Task';
      formLogic.description = 'Detailed description';
      formLogic.priority = 'high';
      formLogic.category = 'Work';
      formLogic.dueDate = '2024-12-31';
      
      const result = formLogic.handleSubmit();
      expect(result).toBe(true);

      const tasks = get(taskStore);
      expect(tasks.length).toBe(1);
      expect(tasks[0].title).toBe('Complete Task');
      expect(tasks[0].description).toBe('Detailed description');
      expect(tasks[0].priority).toBe('high');
      expect(tasks[0].category).toBe('Work');
      expect(tasks[0].dueDate).toEqual(new Date('2024-12-31'));
      expect(tasks[0].completed).toBe(false);
      expect(tasks[0].bananaReward).toBeGreaterThan(0);
    });

    it('should trim whitespace from title and description', () => {
      formLogic.title = '  Test Task  ';
      formLogic.description = '  Test Description  ';
      formLogic.category = '  Work  ';
      
      formLogic.handleSubmit();

      const tasks = get(taskStore);
      expect(tasks[0].title).toBe('Test Task');
      expect(tasks[0].description).toBe('Test Description');
      expect(tasks[0].category).toBe('Work');
    });

    it('should reset form after successful submission', () => {
      formLogic.title = 'Test Task';
      formLogic.description = 'Test Description';
      formLogic.priority = 'high';
      formLogic.category = 'Work';
      formLogic.dueDate = '2024-12-31';
      
      formLogic.handleSubmit();

      expect(formLogic.title).toBe('');
      expect(formLogic.description).toBe('');
      expect(formLogic.priority).toBe('medium');
      expect(formLogic.category).toBe('');
      expect(formLogic.dueDate).toBe('');
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('should submit form on Ctrl+Enter', () => {
      formLogic.title = 'Test Task';
      
      const result = formLogic.handleKeydown('Enter', true, false);
      expect(result).toBe(true);
      expect(get(taskStore).length).toBe(1);
    });

    it('should submit form on Cmd+Enter (Mac)', () => {
      formLogic.title = 'Test Task';
      
      const result = formLogic.handleKeydown('Enter', false, true);
      expect(result).toBe(true);
      expect(get(taskStore).length).toBe(1);
    });

    it('should not submit on Enter without modifier keys', () => {
      formLogic.title = 'Test Task';
      
      const result = formLogic.handleKeydown('Enter', false, false);
      expect(result).toBe(false);
      expect(get(taskStore).length).toBe(0);
    });

    it('should not submit on other keys with modifiers', () => {
      formLogic.title = 'Test Task';
      
      const result = formLogic.handleKeydown('Space', true, false);
      expect(result).toBe(false);
      expect(get(taskStore).length).toBe(0);
    });

    it('should not submit via keyboard if title is empty', () => {
      const result = formLogic.handleKeydown('Enter', true, false);
      expect(result).toBe(false);
      expect(get(taskStore).length).toBe(0);
    });
  });

  describe('Priority Handling', () => {
    it('should handle all priority levels', () => {
      const priorities: Array<'low' | 'medium' | 'high'> = ['low', 'medium', 'high'];
      
      priorities.forEach((priority, index) => {
        formLogic.title = `Task ${index + 1}`;
        formLogic.priority = priority;
        formLogic.handleSubmit();
      });

      const tasks = get(taskStore);
      expect(tasks.length).toBe(3);
      expect(tasks[0].priority).toBe('low');
      expect(tasks[1].priority).toBe('medium');
      expect(tasks[2].priority).toBe('high');
    });

    it('should calculate different rewards for different priorities', () => {
      formLogic.title = 'Test Task';
      
      formLogic.priority = 'low';
      const lowReward = formLogic.bananaReward;
      
      formLogic.priority = 'medium';
      const mediumReward = formLogic.bananaReward;
      
      formLogic.priority = 'high';
      const highReward = formLogic.bananaReward;

      expect(highReward).toBeGreaterThan(mediumReward);
      expect(mediumReward).toBeGreaterThan(lowReward);
    });
  });

  describe('Date Handling', () => {
    it('should handle valid date strings', () => {
      formLogic.title = 'Test Task';
      formLogic.dueDate = '2024-12-31';
      
      formLogic.handleSubmit();

      const tasks = get(taskStore);
      expect(tasks[0].dueDate).toEqual(new Date('2024-12-31'));
    });

    it('should handle empty date', () => {
      formLogic.title = 'Test Task';
      formLogic.dueDate = '';
      
      formLogic.handleSubmit();

      const tasks = get(taskStore);
      expect(tasks[0].dueDate).toBeUndefined();
    });

    it('should affect banana reward calculation', () => {
      formLogic.title = 'Test Task';
      
      const rewardWithoutDate = formLogic.bananaReward;
      
      formLogic.dueDate = '2024-12-31';
      const rewardWithDate = formLogic.bananaReward;

      // Tasks with due dates typically get bonus rewards
      expect(rewardWithDate).toBeGreaterThanOrEqual(rewardWithoutDate);
    });
  });

  describe('Edge Cases', () => {
    it('should handle very long titles and descriptions', () => {
      const longTitle = 'A'.repeat(1000);
      const longDescription = 'B'.repeat(5000);
      
      formLogic.title = longTitle;
      formLogic.description = longDescription;
      
      formLogic.handleSubmit();

      const tasks = get(taskStore);
      expect(tasks[0].title).toBe(longTitle);
      expect(tasks[0].description).toBe(longDescription);
    });

    it('should handle special characters in input', () => {
      formLogic.title = '🎯 Special Task! @#$%^&*()';
      formLogic.description = 'Description with émojis 🚀 and ñ characters';
      formLogic.category = 'Wörk & Stüff';
      
      formLogic.handleSubmit();

      const tasks = get(taskStore);
      expect(tasks[0].title).toBe('🎯 Special Task! @#$%^&*()');
      expect(tasks[0].description).toBe('Description with émojis 🚀 and ñ characters');
      expect(tasks[0].category).toBe('Wörk & Stüff');
    });

    it('should handle rapid form submissions', () => {
      for (let i = 0; i < 10; i++) {
        formLogic.title = `Task ${i}`;
        formLogic.handleSubmit();
      }

      const tasks = get(taskStore);
      expect(tasks.length).toBe(10);
      tasks.forEach((task, index) => {
        expect(task.title).toBe(`Task ${index}`);
      });
    });
  });
});
